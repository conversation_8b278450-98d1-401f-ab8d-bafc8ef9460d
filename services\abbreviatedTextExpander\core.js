/********************************************************************
 *  core.js - Abbreviated Text Expander Core Module
 *  ---------------------------------------------------------------
 *  Main initialization and coordination for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import * as state from '../../state.js';
import { downloadBlob } from '../../utils.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL, GEMINI_API_KEY } from '../../constants.js';
import { initializeAI, testGeminiConnection } from './ai.js';
import { initializeTextProcessing } from './textProcessing.js';
import { initializeUIHandlers } from './uiHandlers.js';
import { initializeDropdowns } from './utils.js';

// Current document state
export let currentDocument = null;
export let selectedText = '';
export let selectedRange = null;
export let geminiApiKey = '';
export let documentHistory = [];
export let historyIndex = -1;
export let isSelecting = false;
export let selectionStartPos = null;

// Initialize with environment variable if available
if (GEMINI_API_KEY && !geminiApiKey) {
    geminiApiKey = GEMINI_API_KEY;
}

/**
 * Initialize the Abbreviated Text Expander
 */
export function initializeAbbreviatedTextExpander() {
    console.log('Initializing Abbreviated Text Expander...');
    
    // Initialize all modules
    initializeAI();
    initializeTextProcessing();
    initializeUIHandlers();
    initializeDropdowns();
    
    // Set up document loading
    setupDocumentHandling();
    
    // Initialize prompt template and model
    updateInstructionsPreview();
    
    console.log('Abbreviated Text Expander initialized successfully');
}

/**
 * Set up document handling
 */
function setupDocumentHandling() {
    // Listen for document changes from the main application
    document.addEventListener('documentLoaded', (event) => {
        handleDocumentLoaded(event.detail);
    });

    document.addEventListener('documentClosed', () => {
        handleDocumentClosed();
    });

    // Set up file input handler
    if (dom.abbreviatedTextExpanderFileInput) {
        dom.abbreviatedTextExpanderFileInput.addEventListener('change', handleFileInput);
    }

    // Set up save button handler
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.addEventListener('click', handleSaveDocument);
    }
}

/**
 * Handle document loaded
 */
export function handleDocumentLoaded(document) {
    currentDocument = document;
    console.log('Document loaded in Abbreviated Text Expander:', document?.name);
    
    // Reset history
    documentHistory = [];
    historyIndex = -1;
    
    // Update button states
    updateButtonStates();
    
    // Set up text area if available
    if (dom.abbreviatedTextExpanderTextArea) {
        setupTextAreaHandling();
    }
}

/**
 * Handle document closed
 */
export function handleDocumentClosed() {
    currentDocument = null;
    selectedText = '';
    selectedRange = null;
    documentHistory = [];
    historyIndex = -1;

    console.log('Document closed in Abbreviated Text Expander');
    updateButtonStates();
}

/**
 * Handle file input change
 */
async function handleFileInput(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.docx')) {
        updateStatus("Please select a DOCX file.", "error");
        return;
    }

    try {
        updateStatus("Loading document...", "info");

        // Use mammoth.js to extract text from DOCX
        const arrayBuffer = await file.arrayBuffer();
        const result = await window.mammoth.extractRawText({ arrayBuffer });

        if (result.value) {
            // Create document object
            const document = {
                file: file,
                originalText: result.value,
                name: file.name,
                modified: false
            };

            // Load the document
            handleDocumentLoaded(document);

            // Set the text content
            if (dom.abbreviatedTextExpanderTextArea) {
                dom.abbreviatedTextExpanderTextArea.textContent = result.value;
            }

            // Update file info
            if (dom.abbreviatedTextExpanderFileInfo) {
                dom.abbreviatedTextExpanderFileInfo.textContent = `Document loaded: ${file.name}`;
            }

            updateStatus("Document loaded successfully.", "success");
        } else {
            updateStatus("Failed to extract text from document.", "error");
        }
    } catch (error) {
        console.error('Error loading document:', error);
        updateStatus(`Failed to load document: ${error.message}`, "error");
    }
}

/**
 * Handle save document
 */
async function handleSaveDocument() {
    if (!currentDocument) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Saving document...", "info");

        // Get the current text content
        const textContent = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;

        // Create DOCX from text using html-docx-js
        const htmlContent = textContent.split('\n').map(line => `<p>${line || '&nbsp;'}</p>`).join('');

        const fullHtml = `
            <!DOCTYPE html>
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <meta charset="utf-8">
                <title>Document</title>
                <style>
                    body {
                        font-family: 'Times New Roman', serif;
                        font-size: 12pt;
                        line-height: 1.15;
                        margin: 1in;
                    }
                    p {
                        margin: 0;
                        padding: 0;
                        margin-bottom: 6pt;
                    }
                </style>
            </head>
            <body>
                ${htmlContent}
            </body>
            </html>
        `;

        // Convert HTML to DOCX
        const docxBlob = window.htmlDocx.asBlob(fullHtml, {
            orientation: 'portrait',
            margins: {
                top: 720,    // 1 inch = 720 twips
                right: 720,
                bottom: 720,
                left: 720
            }
        });

        // Download the file
        downloadBlob(docxBlob, currentDocument.name);

        // Mark as not modified
        currentDocument.modified = false;
        updateButtonStates();

        updateStatus("Document saved successfully.", "success");
    } catch (error) {
        console.error('Error saving document:', error);
        updateStatus(`Failed to save document: ${error.message}`, "error");
    }
}

/**
 * Set up text area handling
 */
function setupTextAreaHandling() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Add event listeners for text selection
    dom.abbreviatedTextExpanderTextArea.addEventListener('mousedown', handleMouseDown);
    dom.abbreviatedTextExpanderTextArea.addEventListener('mouseup', handleMouseUp);
    dom.abbreviatedTextExpanderTextArea.addEventListener('selectstart', handleSelectionStart);
    dom.abbreviatedTextExpanderTextArea.addEventListener('input', handleDocumentChange);

    // Set up toolbar button event listeners
    setupToolbarButtons();
}

/**
 * Set up toolbar button event listeners
 */
function setupToolbarButtons() {
    if (dom.selectAllTextBtn) {
        dom.selectAllTextBtn.addEventListener('click', handleSelectAll);
    }

    if (dom.clearSelectionBtn) {
        dom.clearSelectionBtn.addEventListener('click', handleClearSelection);
    }

    if (dom.undoChangesBtn) {
        dom.undoChangesBtn.addEventListener('click', handleUndo);
    }

    if (dom.redoChangesBtn) {
        dom.redoChangesBtn.addEventListener('click', handleRedo);
    }
}

/**
 * Handle mouse down - start of selection
 */
function handleMouseDown(event) {
    isSelecting = true;
    selectionStartPos = getCaretPosition(event);
}

/**
 * Handle mouse up - end of selection
 */
function handleMouseUp() {
    if (isSelecting) {
        isSelecting = false;
        selectionStartPos = null;
        
        // Update selected text
        updateSelectedText();
    }
}

/**
 * Handle selection start event
 */
function handleSelectionStart() {
    // Allow text selection to proceed normally
    return true;
}

/**
 * Get caret position from mouse event
 */
function getCaretPosition(event) {
    if (document.caretRangeFromPoint) {
        const range = document.caretRangeFromPoint(event.clientX, event.clientY);
        return range;
    } else if (document.caretPositionFromPoint) {
        const pos = document.caretPositionFromPoint(event.clientX, event.clientY);
        if (pos) {
            const range = document.createRange();
            range.setStart(pos.offsetNode, pos.offset);
            return range;
        }
    }
    return null;
}

/**
 * Update selected text
 */
function updateSelectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
        selectedRange = selection.getRangeAt(0);
        selectedText = selectedRange.toString().trim();
        
        console.log('Selected text:', selectedText ? `"${selectedText}"` : 'none');
        updateButtonStates();
    }
}

/**
 * Handle document content changes
 */
export function handleDocumentChange() {
    if (!currentDocument) return;
    
    // Add to history for undo functionality
    const currentContent = dom.abbreviatedTextExpanderTextArea?.textContent || '';
    
    // Only add to history if content actually changed
    if (documentHistory.length === 0 || documentHistory[documentHistory.length - 1] !== currentContent) {
        // Remove any history after current position (for redo functionality)
        documentHistory = documentHistory.slice(0, historyIndex + 1);
        
        // Add new state
        documentHistory.push(currentContent);
        historyIndex = documentHistory.length - 1;
        
        // Limit history size
        if (documentHistory.length > 50) {
            documentHistory.shift();
            historyIndex--;
        }
    }
    
    // Mark document as modified
    if (currentDocument) {
        currentDocument.modified = true;
    }
}

/**
 * Update button states based on current state
 */
export function updateButtonStates() {
    const hasDocument = !!currentDocument;
    const hasSelection = selectedText.length > 0;
    const hasApiKey = geminiApiKey.length > 0;
    
    // AI expansion button (requires document, selection, and API key)
    if (dom.expandAbbreviatedTextBtn) {
        dom.expandAbbreviatedTextBtn.disabled = !hasDocument || !hasSelection || !hasApiKey;
    }
    
    // Test connection button (requires API key)
    if (dom.testGeminiConnectionBtn) {
        dom.testGeminiConnectionBtn.disabled = !hasApiKey;
    }
    
    // Highlight abbreviations button
    if (dom.highlightAbbreviationsBtn) {
        dom.highlightAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Scripture formatting button
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.disabled = !hasDocument;
    }
    
    // Quote formatting button
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.disabled = !hasDocument;
    }
    
    // Abbreviation expansion button
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Papal saints formatting button
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.disabled = !hasDocument;
    }
    
    // Footnotes formatting button
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.disabled = !hasDocument;
    }
    
    // Run all formatting button
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = !hasDocument;
    }

    // Save button (requires document)
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.disabled = !hasDocument;
    }

    // Undo/Redo buttons
    if (dom.undoChangesBtn) {
        dom.undoChangesBtn.disabled = !hasDocument || historyIndex <= 0;
    }

    if (dom.redoChangesBtn) {
        dom.redoChangesBtn.disabled = !hasDocument || historyIndex >= documentHistory.length - 1;
    }
}

/**
 * Update instructions preview
 */
export function updateInstructionsPreview() {
    // This will be implemented in the AI module
    // For now, just a placeholder
    if (dom.currentInstructionsPreview) {
        dom.currentInstructionsPreview.innerHTML = '<p>Instructions preview will be updated by AI module</p>';
    }
}

/**
 * Update status message
 */
export function updateStatus(message, type = 'info') {
    console.log(`Status (${type}):`, message);

    // Debug: Check if status bar element exists
    console.log('Status bar element found:', !!dom.abbreviatedTextExpanderStatusBar);
    if (dom.abbreviatedTextExpanderStatusBar) {
        console.log('Status bar current text:', dom.abbreviatedTextExpanderStatusBar.textContent);
        console.log('Setting status bar to:', message);
    }

    // Update status in the abbreviated text expander status bar
    if (dom.abbreviatedTextExpanderStatusBar) {
        dom.abbreviatedTextExpanderStatusBar.textContent = message;
        dom.abbreviatedTextExpanderStatusBar.className = `status-bar ${type}`;

        // Add visual styling based on type
        dom.abbreviatedTextExpanderStatusBar.style.display = 'block';
        dom.abbreviatedTextExpanderStatusBar.style.visibility = 'visible';

        // Auto-hide after 5 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                dom.abbreviatedTextExpanderStatusBar.textContent = 'Abbreviated Text Expander Ready.';
                dom.abbreviatedTextExpanderStatusBar.className = 'status-bar';
            }, 5000);
        }
    } else {
        console.warn('Status bar element not found! Cannot display status message:', message);
    }
}

// Export state setters for other modules
export function setCurrentDocument(doc) { currentDocument = doc; }
export function setSelectedText(text) { selectedText = text; }
export function setSelectedRange(range) { selectedRange = range; }
export function setGeminiApiKey(key) { geminiApiKey = key; }
export function addToHistory(content) { 
    documentHistory.push(content);
    historyIndex = documentHistory.length - 1;
}

/**
 * Handle select all button
 */
function handleSelectAll() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Select all text in the editor
    const range = document.createRange();
    range.selectNodeContents(dom.abbreviatedTextExpanderTextArea);

    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    // Update selected text
    updateSelectedText();

    console.log('Selected all text');
}

/**
 * Handle clear selection button
 */
function handleClearSelection() {
    // Clear the selection
    const selection = window.getSelection();
    selection.removeAllRanges();

    // Reset selected text state
    selectedText = '';
    selectedRange = null;

    // Update button states
    updateButtonStates();

    console.log('Cleared text selection');
}

/**
 * Handle undo button
 */
function handleUndo() {
    if (historyIndex > 0) {
        historyIndex--;
        const previousContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = previousContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Undo applied, history index:', historyIndex);
    }
}

/**
 * Handle redo button
 */
function handleRedo() {
    if (historyIndex < documentHistory.length - 1) {
        historyIndex++;
        const nextContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = nextContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Redo applied, history index:', historyIndex);
    }
}

// Export state getters for other modules
export function getCurrentDocument() { return currentDocument; }
export function getSelectedText() { return selectedText; }
export function getSelectedRange() { return selectedRange; }
export function getGeminiApiKey() { return geminiApiKey; }
export function getDocumentHistory() { return documentHistory; }
export function getHistoryIndex() { return historyIndex; }
