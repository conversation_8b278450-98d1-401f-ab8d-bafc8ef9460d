/********************************************************************
 *  textProcessing.js - Text Processing Module
 *  ---------------------------------------------------------------
 *  All text formatting and processing functions
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, getGeminiApiKey, updateStatus, handleDocumentChange } from './core.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL } from '../../constants.js';

/**
 * Initialize text processing
 */
export function initializeTextProcessing() {
    console.log('Initializing text processing...');
    
    // Set up event listeners for all formatting buttons
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.addEventListener('click', handleFormatScripture);
    }
    
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.addEventListener('click', handleFormatQuotes);
    }
    
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.addEventListener('click', handleExpandAbbreviations);
    }
    
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.addEventListener('click', handleFormatPapalSaints);
    }
    
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.addEventListener('click', handleFormatFootnotes);
    }

    // Set up modal event listeners
    setupModalEventListeners();
}

/**
 * Set up event listeners for the preview modal
 */
function setupModalEventListeners() {
    const acceptBtn = document.getElementById('accept-expansion-btn');
    const rejectBtn = document.getElementById('reject-expansion-btn');

    if (acceptBtn) {
        acceptBtn.addEventListener('click', handleAcceptFormatting);
    }

    if (rejectBtn) {
        rejectBtn.addEventListener('click', handleRejectFormatting);
    }
}

/**
 * Handle accepting formatting changes from the modal
 */
function handleAcceptFormatting() {
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (expandedDisplay && modal) {
        // Get the formatted text (either from dataset or from the display)
        const formattedText = expandedDisplay.dataset.markdownText || expandedDisplay.textContent;

        if (formattedText) {
            // Apply the formatting
            replaceSelectedTextWithMarkdown(formattedText);

            // Close modal and clear comparison view
            modal.style.display = 'none';
            clearComparisonView();

            updateStatus("Formatting applied successfully.", "success");
        }
    }
}

/**
 * Handle rejecting formatting changes from the modal
 */
function handleRejectFormatting() {
    const modal = document.getElementById('text-expansion-preview-modal');

    if (modal) {
        // Close modal and clear comparison view
        modal.style.display = 'none';
        clearComparisonView();

        updateStatus("Formatting changes rejected.", "info");
    }
}

/**
 * Handle scripture formatting
 */
async function handleFormatScripture() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references...", "info");

        // Get the selected text
        const selectedText = getSelectedText();

        if (!selectedText) {
            updateStatus("Please select some text to format scripture references.", "error");
            return;
        }

        // Use AI to format scripture references
        await formatScriptureWithAI(selectedText);

    } catch (error) {
        console.error("Error formatting scripture:", error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Handle quote formatting
 */
function handleFormatQuotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting quotes from written works...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply quote formatting
        const formattedContent = formatQuotesFromWrittenWorks(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.innerHTML = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Quotes from written works formatted successfully.", "success");
        } else {
            updateStatus("No qualifying quotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting quotes:", error);
        updateStatus(`Failed to format quotes: ${error.message}`, "error");
    }
}

/**
 * Handle abbreviation expansion
 */
function handleExpandAbbreviations() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Expanding abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply abbreviation expansion
        const expandedContent = expandAllAbbreviations(content);
        
        if (expandedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = expandedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Abbreviations expanded successfully.", "success");
        } else {
            updateStatus("No abbreviations found to expand.", "info");
        }
    } catch (error) {
        console.error("Error expanding abbreviations:", error);
        updateStatus(`Failed to expand abbreviations: ${error.message}`, "error");
    }
}

/**
 * Handle papal saints formatting
 */
function handleFormatPapalSaints() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting papal names and saint abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply papal saints formatting
        const formattedContent = formatPapalNamesAndSaints(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Papal names and saint abbreviations formatted successfully.", "success");
        } else {
            updateStatus("No papal names or saint abbreviations found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting papal names and saints:", error);
        updateStatus(`Failed to format papal names and saints: ${error.message}`, "error");
    }
}

/**
 * Handle footnote formatting
 */
function handleFormatFootnotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting footnotes for audiobook...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply footnote formatting
        const formattedContent = formatFootnotesForAudiobook(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Footnotes formatted for audiobook successfully.", "success");
        } else {
            updateStatus("No footnotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting footnotes:", error);
        updateStatus(`Failed to format footnotes: ${error.message}`, "error");
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // If the formatted text contains HTML, we need to parse it
        if (formattedText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(formattedText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Text replaced successfully');
    }
}

/**
 * Replace selected text with markdown-formatted text (converts **bold** to HTML)
 */
function replaceSelectedTextWithMarkdown(markdownText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // Convert markdown bold (**text**) to HTML
        let htmlText = markdownText;

        // Replace **text** with <strong>text</strong>
        htmlText = htmlText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // If the text contains HTML, parse it
        if (htmlText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(htmlText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Markdown text replaced successfully');
    }
}

/**
 * Format scripture references using AI
 */
async function formatScriptureWithAI(selectedText) {
    const apiKey = getGeminiApiKey();

    if (!apiKey) {
        updateStatus("Please set your Gemini API key first.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references with AI...", "info");

        // Get the selected model
        const selectedModel = dom.abbreviatedTextExpanderModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];

        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }

        // Build the prompt for scripture formatting
        const prompt = `Please format the following text according to these specific rules:

1. If the text contains a scripture quote with 8 or more words followed by a biblical reference in parentheses:
   - Add "quote," at the beginning of the quoted text
   - Add "end quote," at the end of the quoted text
   - Expand any abbreviated book names to their full names (e.g., "1 Pet." → "First Peter", "Lev." → "Leviticus", "2 Cor." → "Second Corinthians")
   - Convert numbered books to spelled-out ordinals: "1" → "First", "2" → "Second", "3" → "Third"
   - Format chapter and verse numbers as "chapter X, verse Y" (e.g., "17, 11" → "chapter 17, verse 11")
   - Make the entire result bold by wrapping it in **bold formatting**

2. If the text does not contain a scripture reference or the quote has fewer than 8 words, return it unchanged.

Book name conversion examples:
- "1 Pet." or "1 Peter" → "First Peter"
- "2 Cor." or "2 Corinthians" → "Second Corinthians"
- "1 Sam." or "1 Samuel" → "First Samuel"
- "2 Kings" → "Second Kings"
- "3 John" → "Third John"
- "Lev." → "Leviticus"
- "Gen." → "Genesis"

Examples:
- Input: "'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' (1 Pet. 1, 18)"
- Output: **quote, You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled. end quote, (First Peter, chapter 1, verse 18)**

- Input: "'because the life of the flesh is in the blood: and I have given it to you, that you may make atone­ment with it upon the altar for your souls: and the blood may be for an expiation of the soul.' (Lev. 17, 11)"
- Output: **quote, because the life of the flesh is in the blood: and I have given it to you, that you may make atone­ment with it upon the altar for your souls: and the blood may be for an expiation of the soul. end quote, (Leviticus, chapter 17, verse 11)**

Text to format:
${selectedText}

Return only the formatted text, nothing else.`;

        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 0.8,
                    maxOutputTokens: 1000,
                }
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const formattedText = data.candidates[0].content.parts[0].text.trim();

            // Show the preview modal for comparison
            showScriptureFormattingPreview(selectedText, formattedText);

            updateStatus(`✅ Scripture formatting preview ready - review and accept/reject changes`, "success");
        } else {
            updateStatus("⚠️ Received unexpected response format from AI.", "warning");
        }

    } catch (error) {
        console.error('Error formatting scripture with AI:', error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Show scripture formatting preview modal
 */
function showScriptureFormattingPreview(originalText, formattedText) {
    // Update the preview modal content
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal.querySelector('h3');

    if (originalDisplay && expandedDisplay && modal && modalTitle) {
        // Update modal title
        modalTitle.textContent = 'Scripture Formatting Preview';

        // Show original text
        originalDisplay.textContent = originalText;

        // Show formatted text (convert markdown to HTML for display)
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        expandedDisplay.innerHTML = htmlFormattedText;

        // Store the formatted text for later use
        expandedDisplay.dataset.markdownText = formattedText;

        // Show the modal
        modal.style.display = 'block';

        // Update the selection display as well
        updateSelectionDisplayWithComparison(originalText, formattedText);
    }
}

/**
 * Update the selection display to show comparison
 */
function updateSelectionDisplayWithComparison(originalText, formattedText) {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        selectionDisplay.innerHTML = `
            <div class="comparison-view">
                <div class="comparison-section">
                    <h4>Original Selected Text:</h4>
                    <div class="text-preview original">${highlightedOriginal}</div>
                </div>
                <div class="comparison-section">
                    <h4>AI Formatted Result:</h4>
                    <div class="text-preview formatted">${highlightedFormatted}</div>
                </div>
                <div class="comparison-actions">
                    <button id="accept-scripture-formatting" class="control-button accent-button small">Accept Formatting</button>
                    <button id="reject-scripture-formatting" class="control-button secondary small">Reject Changes</button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        const acceptBtn = document.getElementById('accept-scripture-formatting');
        const rejectBtn = document.getElementById('reject-scripture-formatting');

        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                replaceSelectedTextWithMarkdown(formattedText);
                clearComparisonView();
                updateStatus("Scripture formatting applied successfully.", "success");
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                clearComparisonView();
                updateStatus("Scripture formatting rejected.", "info");
            });
        }
    }
}

/**
 * Highlight differences between original and formatted text
 */
function highlightTextDifferences(originalText, formattedText) {
    // Remove HTML tags from formatted text for comparison
    const cleanFormattedText = formattedText.replace(/<[^>]*>/g, '');

    // Split texts into words for comparison
    const originalWords = originalText.split(/(\s+|[^\w\s])/);
    const formattedWords = cleanFormattedText.split(/(\s+|[^\w\s])/);

    // Find differences using a simple word-by-word comparison
    const originalHighlighted = [];
    const formattedHighlighted = [];

    let originalIndex = 0;
    let formattedIndex = 0;

    // Handle the case where formatted text has additions at the beginning (like "quote,")
    if (formattedWords.length > originalWords.length) {
        // Check if formatted text starts with new content
        const potentialAddition = formattedWords.slice(0, formattedWords.length - originalWords.length).join('');
        if (potentialAddition.toLowerCase().includes('quote')) {
            // Highlight the added beginning
            const addedPart = formattedWords.slice(0, formattedWords.length - originalWords.length).join('');
            formattedHighlighted.push(`<span class="diff-added">${addedPart}</span>`);
            formattedIndex = formattedWords.length - originalWords.length;
        }
    }

    // Compare word by word
    while (originalIndex < originalWords.length && formattedIndex < formattedWords.length) {
        const originalWord = originalWords[originalIndex];
        const formattedWord = formattedWords[formattedIndex];

        if (originalWord === formattedWord) {
            // Words match - no highlighting needed
            originalHighlighted.push(originalWord);
            formattedHighlighted.push(formattedWord);
            originalIndex++;
            formattedIndex++;
        } else {
            // Words differ - check for common scripture formatting patterns
            const originalLower = originalWord.toLowerCase();
            const formattedLower = formattedWord.toLowerCase();

            // Check for book name expansions (e.g., "1 Pet." → "First Peter")
            if (isBookNameExpansion(originalWord, formattedWord)) {
                originalHighlighted.push(`<span class="diff-changed">${originalWord}</span>`);
                formattedHighlighted.push(`<span class="diff-changed">${formattedWord}</span>`);
                originalIndex++;
                formattedIndex++;
            }
            // Check for chapter/verse formatting (e.g., "17, 11" → "chapter 17, verse 11")
            else if (isChapterVerseExpansion(originalWords, formattedWords, originalIndex, formattedIndex)) {
                // Handle chapter/verse expansion
                const originalPart = originalWords.slice(originalIndex, originalIndex + 3).join(''); // "17, 11"
                const formattedPart = formattedWords.slice(formattedIndex, formattedIndex + 5).join(''); // "chapter 17, verse 11"

                originalHighlighted.push(`<span class="diff-changed">${originalPart}</span>`);
                formattedHighlighted.push(`<span class="diff-changed">${formattedPart}</span>`);

                originalIndex += 3;
                formattedIndex += 5;
            }
            // Default case - highlight as different
            else {
                originalHighlighted.push(`<span class="diff-changed">${originalWord}</span>`);
                formattedHighlighted.push(`<span class="diff-changed">${formattedWord}</span>`);
                originalIndex++;
                formattedIndex++;
            }
        }
    }

    // Handle remaining words (additions at the end)
    while (formattedIndex < formattedWords.length) {
        const remainingWord = formattedWords[formattedIndex];
        if (remainingWord.toLowerCase().includes('end quote') || remainingWord.toLowerCase().includes('chapter') || remainingWord.toLowerCase().includes('verse')) {
            formattedHighlighted.push(`<span class="diff-added">${remainingWord}</span>`);
        } else {
            formattedHighlighted.push(`<span class="diff-changed">${remainingWord}</span>`);
        }
        formattedIndex++;
    }

    return {
        highlightedOriginal: originalHighlighted.join(''),
        highlightedFormatted: formattedHighlighted.join('')
    };
}

/**
 * Check if this is a book name expansion (e.g., "1 Pet." → "First Peter")
 */
function isBookNameExpansion(original, formatted) {
    const bookMappings = {
        '1': 'first', '2': 'second', '3': 'third',
        'pet.': 'peter', 'pet': 'peter',
        'cor.': 'corinthians', 'cor': 'corinthians',
        'sam.': 'samuel', 'sam': 'samuel',
        'lev.': 'leviticus', 'lev': 'leviticus',
        'gen.': 'genesis', 'gen': 'genesis',
        'tim.': 'timothy', 'tim': 'timothy',
        'thess.': 'thessalonians', 'thess': 'thessalonians'
    };

    const originalLower = original.toLowerCase();
    const formattedLower = formatted.toLowerCase();

    return bookMappings[originalLower] === formattedLower;
}

/**
 * Check if this is a chapter/verse expansion
 */
function isChapterVerseExpansion(originalWords, formattedWords, originalIndex, formattedIndex) {
    // Check if we have enough words to compare
    if (originalIndex + 2 >= originalWords.length || formattedIndex + 4 >= formattedWords.length) {
        return false;
    }

    // Look for pattern: number, comma, number → "chapter", number, comma, "verse", number
    const original = originalWords.slice(originalIndex, originalIndex + 3).join('').toLowerCase();
    const formatted = formattedWords.slice(formattedIndex, formattedIndex + 5).join('').toLowerCase();

    // Simple pattern matching for chapter/verse
    return /^\d+,\s*\d+$/.test(original.replace(/\s/g, '')) &&
           formatted.includes('chapter') && formatted.includes('verse');
}

/**
 * Clear the comparison view and return to normal selection display
 */
function clearComparisonView() {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (selectionDisplay) {
        selectionDisplay.innerHTML = `
            <div class="no-selection-state">
                <p>No text selected</p>
                <p class="hint">Select text in the document to expand abbreviations</p>
            </div>
        `;
    }

    if (modal) {
        modal.style.display = 'none';
    }
}

// Placeholder functions - these will be implemented in separate files
// due to their complexity and size

/**
 * Format scripture references according to the specified rules
 */
export function formatScriptureReferences(text) {
    console.log('Formatting scripture references...');
    console.log('Input text:', JSON.stringify(text));

    // Bible book abbreviations mapping to full names
    const bookAbbreviations = {
        // Old Testament
        'Gen.': 'Genesis', 'Gen': 'Genesis',
        'Ex.': 'Exodus', 'Exod.': 'Exodus', 'Exod': 'Exodus',
        'Lev.': 'Leviticus', 'Lev': 'Leviticus',
        'Num.': 'Numbers', 'Num': 'Numbers',
        'Deut.': 'Deuteronomy', 'Deut': 'Deuteronomy',
        'Josh.': 'Joshua', 'Josh': 'Joshua',
        'Judg.': 'Judges', 'Judg': 'Judges',
        'Ruth': 'Ruth',
        '1 Sam.': '1 Samuel', '1Sam.': '1 Samuel', '1Sam': '1 Samuel',
        '2 Sam.': '2 Samuel', '2Sam.': '2 Samuel', '2Sam': '2 Samuel',
        '1 Kings': '1 Kings', '1Kgs.': '1 Kings', '1Kgs': '1 Kings',
        '2 Kings': '2 Kings', '2Kgs.': '2 Kings', '2Kgs': '2 Kings',
        '1 Chr.': '1 Chronicles', '1Chr.': '1 Chronicles', '1Chr': '1 Chronicles',
        '2 Chr.': '2 Chronicles', '2Chr.': '2 Chronicles', '2Chr': '2 Chronicles',
        'Ezra': 'Ezra',
        'Neh.': 'Nehemiah', 'Neh': 'Nehemiah',
        'Esth.': 'Esther', 'Esth': 'Esther',
        'Job': 'Job',
        'Ps.': 'Psalms', 'Ps': 'Psalms', 'Psa.': 'Psalms', 'Psa': 'Psalms',
        'Prov.': 'Proverbs', 'Prov': 'Proverbs',
        'Eccl.': 'Ecclesiastes', 'Eccl': 'Ecclesiastes', 'Ecc.': 'Ecclesiastes', 'Ecc': 'Ecclesiastes',
        'Song': 'Song of Songs', 'SS': 'Song of Songs',
        'Is.': 'Isaiah', 'Isa.': 'Isaiah', 'Isa': 'Isaiah',
        'Jer.': 'Jeremiah', 'Jer': 'Jeremiah',
        'Lam.': 'Lamentations', 'Lam': 'Lamentations',
        'Ezek.': 'Ezekiel', 'Ezek': 'Ezekiel', 'Ez.': 'Ezekiel', 'Ez': 'Ezekiel',
        'Dan.': 'Daniel', 'Dan': 'Daniel',
        'Hos.': 'Hosea', 'Hos': 'Hosea',
        'Joel': 'Joel',
        'Amos': 'Amos',
        'Obad.': 'Obadiah', 'Obad': 'Obadiah',
        'Jon.': 'Jonah', 'Jon': 'Jonah',
        'Mic.': 'Micah', 'Mic': 'Micah',
        'Nah.': 'Nahum', 'Nah': 'Nahum',
        'Hab.': 'Habakkuk', 'Hab': 'Habakkuk',
        'Zeph.': 'Zephaniah', 'Zeph': 'Zephaniah',
        'Hag.': 'Haggai', 'Hag': 'Haggai',
        'Zech.': 'Zechariah', 'Zech': 'Zechariah',
        'Mal.': 'Malachi', 'Mal': 'Malachi',

        // New Testament
        'Mt.': 'Matthew', 'Matt.': 'Matthew', 'Matt': 'Matthew',
        'Mk.': 'Mark', 'Mark': 'Mark',
        'Lk.': 'Luke', 'Luke': 'Luke',
        'Jn.': 'John', 'John': 'John',
        'Acts': 'Acts',
        'Rom.': 'Romans', 'Rom': 'Romans',
        '1 Cor.': '1 Corinthians', '1Cor.': '1 Corinthians', '1Cor': '1 Corinthians',
        '2 Cor.': '2 Corinthians', '2Cor.': '2 Corinthians', '2Cor': '2 Corinthians',
        'Gal.': 'Galatians', 'Gal': 'Galatians',
        'Eph.': 'Ephesians', 'Eph': 'Ephesians',
        'Phil.': 'Philippians', 'Phil': 'Philippians',
        'Col.': 'Colossians', 'Col': 'Colossians',
        '1 Thess.': '1 Thessalonians', '1Thess.': '1 Thessalonians', '1Thess': '1 Thessalonians',
        '2 Thess.': '2 Thessalonians', '2Thess.': '2 Thessalonians', '2Thess': '2 Thessalonians',
        '1 Tim.': '1 Timothy', '1Tim.': '1 Timothy', '1Tim': '1 Timothy',
        '2 Tim.': '2 Timothy', '2Tim.': '2 Timothy', '2Tim': '2 Timothy',
        'Tit.': 'Titus', 'Tit': 'Titus',
        'Philem.': 'Philemon', 'Philem': 'Philemon',
        'Heb.': 'Hebrews', 'Heb': 'Hebrews',
        'Jas.': 'James', 'Jas': 'James',
        '1 Pet.': '1 Peter', '1Pet.': '1 Peter', '1Pet': '1 Peter',
        '2 Pet.': '2 Peter', '2Pet.': '2 Peter', '2Pet': '2 Peter',
        '1 Jn.': '1 John', '1John': '1 John',
        '2 Jn.': '2 John', '2John': '2 John',
        '3 Jn.': '3 John', '3John': '3 John',
        'Jude': 'Jude',
        'Rev.': 'Revelation', 'Rev': 'Revelation'
    };

    // Pattern to match quoted text followed by scripture reference
    // Matches various quote types: 'text' "text" 'text' "text" etc.
    const scripturePattern = /(['""''])((?:(?!\1).){8,}?)\1\s*\(([^)]+)\)/g;

    console.log('Testing pattern against text...');
    console.log('Pattern:', scripturePattern);

    let formattedText = text;
    let hasChanges = false;

    formattedText = formattedText.replace(scripturePattern, (match, quote, quotedText, reference) => {
        // Check if the quoted text has 8 or more words
        const wordCount = quotedText.trim().split(/\s+/).length;
        if (wordCount < 8) {
            return match; // Don't format if less than 8 words
        }

        // Expand book abbreviations in the reference
        let expandedReference = reference;
        for (const [abbrev, fullName] of Object.entries(bookAbbreviations)) {
            const regex = new RegExp(`\\b${abbrev.replace('.', '\\.')}\\b`, 'gi');
            expandedReference = expandedReference.replace(regex, fullName);
        }

        // Format chapter and verse numbers with "chapter" and "verse" words
        // Pattern: BookName number, number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+),?\s*(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number:number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+):(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number -> BookName, chapter number (for chapter-only references)
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+)$/, '$1, chapter $2');

        hasChanges = true;

        // Format as: quote, [quoted text] end quote, ([expanded reference])
        return `<strong>quote, ${quotedText} end quote, (${expandedReference})</strong>`;
    });

    if (hasChanges) {
        console.log('Scripture references formatted successfully');
    } else {
        console.log('No scripture references found to format');
    }

    return formattedText;
}

/**
 * Format quotes from written works according to the specified rules
 */
export function formatQuotesFromWrittenWorks(text) {
    // This function will be moved to a separate quotes.js file
    // For now, return the original text
    console.log('Quote formatting not yet implemented in modular structure');
    return text;
}

/**
 * Expand all abbreviations in the text
 */
export function expandAllAbbreviations(text) {
    // This function will be moved to a separate abbreviations.js file
    // For now, return the original text
    console.log('Abbreviation expansion not yet implemented in modular structure');
    return text;
}

/**
 * Format papal names and saint abbreviations
 */
export function formatPapalNamesAndSaints(text) {
    // This function will be moved to a separate papal.js file
    // For now, return the original text
    console.log('Papal/saint formatting not yet implemented in modular structure');
    return text;
}

/**
 * Format footnotes for audiobook narration
 */
export function formatFootnotesForAudiobook(text) {
    // This function will be moved to a separate footnotes.js file
    // For now, return the original text
    console.log('Footnote formatting not yet implemented in modular structure');
    return text;
}
