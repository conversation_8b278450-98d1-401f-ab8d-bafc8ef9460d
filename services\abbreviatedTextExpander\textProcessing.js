/********************************************************************
 *  textProcessing.js - Text Processing Module
 *  ---------------------------------------------------------------
 *  All text formatting and processing functions
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, getGeminiApiKey, updateStatus, handleDocumentChange } from './core.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL } from '../../constants.js';

/**
 * Initialize text processing
 */
export function initializeTextProcessing() {
    console.log('Initializing text processing...');
    
    // Set up event listeners for all formatting buttons
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.addEventListener('click', handleFormatScripture);
    }
    
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.addEventListener('click', handleFormatQuotes);
    }
    
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.addEventListener('click', handleExpandAbbreviations);
    }
    
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.addEventListener('click', handleFormatPapalSaints);
    }
    
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.addEventListener('click', handleFormatFootnotes);
    }

    // Set up modal event listeners
    setupModalEventListeners();
}

/**
 * Set up event listeners for the preview modal
 */
function setupModalEventListeners() {
    const acceptBtn = document.getElementById('accept-expansion-btn');
    const rejectBtn = document.getElementById('reject-expansion-btn');

    if (acceptBtn) {
        acceptBtn.addEventListener('click', handleAcceptFormatting);
    }

    if (rejectBtn) {
        rejectBtn.addEventListener('click', handleRejectFormatting);
    }
}

/**
 * Handle accepting formatting changes from the modal
 */
function handleAcceptFormatting() {
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (expandedDisplay && modal) {
        // Get the formatted text (either from dataset or from the display)
        const formattedText = expandedDisplay.dataset.markdownText || expandedDisplay.textContent;

        if (formattedText) {
            // Apply the formatting
            replaceSelectedTextWithMarkdown(formattedText);

            // Close modal and clear comparison view
            modal.style.display = 'none';
            clearComparisonView();

            updateStatus("Formatting applied successfully.", "success");
        }
    }
}

/**
 * Handle rejecting formatting changes from the modal
 */
function handleRejectFormatting() {
    const modal = document.getElementById('text-expansion-preview-modal');

    if (modal) {
        // Close modal and clear comparison view
        modal.style.display = 'none';
        clearComparisonView();

        updateStatus("Formatting changes rejected.", "info");
    }
}

/**
 * Handle scripture formatting
 */
async function handleFormatScripture() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references...", "info");

        // Get the selected text
        const selectedText = getSelectedText();

        if (!selectedText) {
            updateStatus("Please select some text to format scripture references.", "error");
            return;
        }

        // Use AI to format scripture references
        await formatScriptureWithAI(selectedText);

    } catch (error) {
        console.error("Error formatting scripture:", error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Handle quote formatting
 */
function handleFormatQuotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting quotes from written works...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply quote formatting
        const formattedContent = formatQuotesFromWrittenWorks(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.innerHTML = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Quotes from written works formatted successfully.", "success");
        } else {
            updateStatus("No qualifying quotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting quotes:", error);
        updateStatus(`Failed to format quotes: ${error.message}`, "error");
    }
}

/**
 * Handle abbreviation expansion
 */
function handleExpandAbbreviations() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Expanding abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply abbreviation expansion
        const expandedContent = expandAllAbbreviations(content);
        
        if (expandedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = expandedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Abbreviations expanded successfully.", "success");
        } else {
            updateStatus("No abbreviations found to expand.", "info");
        }
    } catch (error) {
        console.error("Error expanding abbreviations:", error);
        updateStatus(`Failed to expand abbreviations: ${error.message}`, "error");
    }
}

/**
 * Handle papal saints formatting
 */
function handleFormatPapalSaints() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting papal names and saint abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply papal saints formatting
        const formattedContent = formatPapalNamesAndSaints(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Papal names and saint abbreviations formatted successfully.", "success");
        } else {
            updateStatus("No papal names or saint abbreviations found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting papal names and saints:", error);
        updateStatus(`Failed to format papal names and saints: ${error.message}`, "error");
    }
}

/**
 * Handle footnote formatting
 */
function handleFormatFootnotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting footnotes for audiobook...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply footnote formatting
        const formattedContent = formatFootnotesForAudiobook(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Footnotes formatted for audiobook successfully.", "success");
        } else {
            updateStatus("No footnotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting footnotes:", error);
        updateStatus(`Failed to format footnotes: ${error.message}`, "error");
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // If the formatted text contains HTML, we need to parse it
        if (formattedText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(formattedText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Text replaced successfully');
    }
}

/**
 * Replace selected text with markdown-formatted text (converts **bold** to HTML)
 */
function replaceSelectedTextWithMarkdown(markdownText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // Convert markdown bold (**text**) to HTML
        let htmlText = markdownText;

        // Replace **text** with <strong>text</strong>
        htmlText = htmlText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // If the text contains HTML, parse it
        if (htmlText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(htmlText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Markdown text replaced successfully');
    }
}

/**
 * Format scripture references using AI
 */
async function formatScriptureWithAI(selectedText) {
    const apiKey = getGeminiApiKey();

    if (!apiKey) {
        updateStatus("Please set your Gemini API key first.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references with AI...", "info");

        // Get the selected model
        const selectedModel = dom.abbreviatedTextExpanderModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];

        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }

        // Build the prompt for scripture formatting
        const prompt = `Please format the following text according to these specific rules:

IMPORTANT: The text may contain both regular content AND scripture quotes. Only format the scripture quotes, leave all other text exactly as it is.

1. Look for scripture quotes within the text - these are quoted passages followed by biblical references in parentheses
2. For each scripture quote found:
   - Add "quote," at the beginning of the quoted text
   - Add "end quote," at the end of the quoted text
   - Expand abbreviated book names to full names with ordinals:
     * "1 Pet." → "First Peter", "2 Cor." → "Second Corinthians", "Lev." → "Leviticus"
     * All numbered books: "1" → "First", "2" → "Second", "3" → "Third"
   - Format chapter and verse as "chapter X, verse Y" (e.g., "17, 11" → "chapter 17, verse 11")
   - Make the ENTIRE formatted scripture bold with **bold formatting**
3. Leave all surrounding text completely unchanged
4. Return the full text with only the scripture quotes formatted

Book abbreviations to expand:
- Old Testament: Gen./Genesis, Ex./Exodus, Lev./Leviticus, Num./Numbers, Deut./Deuteronomy, Josh./Joshua, Judg./Judges, 1Sam./First Samuel, 2Sam./Second Samuel, 1Kgs./First Kings, 2Kgs./Second Kings, 1Chr./First Chronicles, 2Chr./Second Chronicles, Ezra, Neh./Nehemiah, Esth./Esther, Job, Ps./Psalms, Prov./Proverbs, Eccl./Ecclesiastes, Song/Song of Songs, Is./Isaiah, Jer./Jeremiah, Lam./Lamentations, Ezek./Ezekiel, Dan./Daniel, Hos./Hosea, Joel, Amos, Obad./Obadiah, Jon./Jonah, Mic./Micah, Nah./Nahum, Hab./Habakkuk, Zeph./Zephaniah, Hag./Haggai, Zech./Zechariah, Mal./Malachi
- New Testament: Mt./Matthew, Mk./Mark, Lk./Luke, Jn./John, Acts, Rom./Romans, 1Cor./First Corinthians, 2Cor./Second Corinthians, Gal./Galatians, Eph./Ephesians, Phil./Philippians, Col./Colossians, 1Thess./First Thessalonians, 2Thess./Second Thessalonians, 1Tim./First Timothy, 2Tim./Second Timothy, Tit./Titus, Philem./Philemon, Heb./Hebrews, Jas./James, 1Pet./First Peter, 2Pet./Second Peter, 1Jn./First John, 2Jn./Second John, 3Jn./Third John, Jude, Rev./Revelation

Book name conversion examples:
- "1 Pet." or "1 Peter" → "First Peter"
- "2 Cor." or "2 Corinthians" → "Second Corinthians"
- "1 Sam." or "1 Samuel" → "First Samuel"
- "2 Kings" → "Second Kings"
- "3 John" → "Third John"
- "Lev." → "Leviticus"
- "Gen." → "Genesis"

Examples:

Example 1 - Mixed content with embedded scripture:
Input: "The blood of Christ is infinitely precious. In the light of the Old Testament, which He fulfilled, the blood of Christ means His whole bodily life. 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' (1 Pet. 1, 18) This was the price of redemption."

Output: "The blood of Christ is infinitely precious. In the light of the Old Testament, which He fulfilled, the blood of Christ means His whole bodily life. **quote, You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled. end quote, (First Peter, chapter 1, verse 18)** This was the price of redemption."

Example 2 - Standalone scripture:
Input: "'the life of the flesh.' (Lev. 17, 11)"
Output: "**quote, the life of the flesh. end quote, (Leviticus, chapter 17, verse 11)**"

Example 3 - Text with no scripture:
Input: "This is a theological discussion without any biblical quotes."
Output: "This is a theological discussion without any biblical quotes."

Text to format:
${selectedText}

Return only the formatted text, nothing else.`;

        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 0.8,
                    maxOutputTokens: 2000,
                }
            })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();

        console.log('AI Response:', data);
        console.log('Full candidates array:', data.candidates);

        if (data.candidates && data.candidates.length > 0) {
            const candidate = data.candidates[0];
            console.log('First candidate:', candidate);
            console.log('Candidate content:', candidate.content);
            if (candidate.content && candidate.content.parts) {
                console.log('Content parts:', candidate.content.parts);
                console.log('First part:', candidate.content.parts[0]);
            }

            // Check different possible response structures
            let formattedText = null;

            // Try the standard structure first
            if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                formattedText = candidate.content.parts[0].text;
                console.log('Found text in standard structure');
            }
            // Try accessing content directly if it's a string
            else if (candidate.content && typeof candidate.content === 'string') {
                formattedText = candidate.content;
                console.log('Found text in candidate.content (string)');
            }
            // Try alternative structure (text directly in candidate)
            else if (candidate.text) {
                formattedText = candidate.text;
                console.log('Found text in candidate.text');
            }
            // Try another alternative (output field)
            else if (candidate.output) {
                formattedText = candidate.output;
                console.log('Found text in candidate.output');
            }
            // Check if there's a message field
            else if (candidate.message && candidate.message.content) {
                formattedText = candidate.message.content;
                console.log('Found text in candidate.message.content');
            }

            if (formattedText && formattedText.trim().length > 0) {
                formattedText = formattedText.trim();
                console.log('Formatted text:', formattedText);

                // Show the preview modal for comparison
                showScriptureFormattingPreview(selectedText, formattedText);

                updateStatus(`✅ Scripture formatting preview ready - review and accept/reject changes`, "success");
            } else {
                console.error('No text content found in any expected location');
                console.error('Candidate structure:', candidate);

                // Check for safety ratings or finish reason
                if (candidate.finishReason) {
                    console.log('Finish reason:', candidate.finishReason);
                    if (candidate.finishReason === 'SAFETY') {
                        updateStatus("⚠️ AI response blocked by safety filters. Try rephrasing your text.", "warning");
                        return;
                    }
                }

                if (candidate.safetyRatings) {
                    console.log('Safety ratings:', candidate.safetyRatings);
                }

                updateStatus("⚠️ AI response missing text content. Check console for details.", "warning");
            }
        } else {
            console.error('No candidates found in response:', data);

            // Check for error messages in the response
            if (data.error) {
                console.error('API Error:', data.error);
                updateStatus(`❌ API Error: ${data.error.message || 'Unknown error'}`, "error");
            } else {
                updateStatus("⚠️ Received unexpected response format from AI.", "warning");
            }
        }

    } catch (error) {
        console.error('Error formatting scripture with AI:', error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Show scripture formatting preview modal
 */
function showScriptureFormattingPreview(originalText, formattedText) {
    // Update the preview modal content
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal.querySelector('h3');

    if (originalDisplay && expandedDisplay && modal && modalTitle) {
        // Update modal title
        modalTitle.textContent = 'Scripture Formatting Preview';

        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        // Show highlighted original text
        originalDisplay.innerHTML = highlightedOriginal;

        // Show highlighted formatted text
        expandedDisplay.innerHTML = highlightedFormatted;

        // Store the formatted text for later use
        expandedDisplay.dataset.markdownText = formattedText;

        // Add color legend to modal if it doesn't exist
        addColorLegendToModal(modal);

        // Show the modal
        modal.style.display = 'block';

        // Update the selection display as well
        updateSelectionDisplayWithComparison(originalText, formattedText);
    }
}

/**
 * Add color legend to modal
 */
function addColorLegendToModal(modal) {
    // Check if legend already exists
    let legend = modal.querySelector('.color-legend');

    if (!legend) {
        // Create legend element
        legend = document.createElement('div');
        legend.className = 'color-legend';
        legend.innerHTML = `
            <div class="legend-title">Change Indicators:</div>
            <div class="legend-items">
                <span class="legend-item">
                    <span class="diff-added">Added</span> - New text like "quote," "end quote," "chapter," "verse"
                </span>
                <span class="legend-item">
                    <span class="diff-changed">Changed</span> - Modified text like "1 Pet." → "First Peter"
                </span>
                <span class="legend-item">
                    <span class="diff-removed">Removed</span> - Deleted text (if any)
                </span>
            </div>
        `;

        // Insert legend after the title
        const title = modal.querySelector('h3');
        if (title) {
            title.insertAdjacentElement('afterend', legend);
        }
    }
}

/**
 * Update the selection display to show comparison
 */
function updateSelectionDisplayWithComparison(originalText, formattedText) {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        selectionDisplay.innerHTML = `
            <div class="comparison-view">
                <div class="color-legend-inline">
                    <div class="legend-title">Change Indicators:</div>
                    <div class="legend-items-inline">
                        <span class="legend-item-inline"><span class="diff-added">Added</span></span>
                        <span class="legend-item-inline"><span class="diff-changed">Changed</span></span>
                        <span class="legend-item-inline"><span class="diff-removed">Removed</span></span>
                    </div>
                </div>
                <div class="comparison-section">
                    <h4>Original Selected Text:</h4>
                    <div class="text-preview original">${highlightedOriginal}</div>
                </div>
                <div class="comparison-section">
                    <h4>AI Formatted Result:</h4>
                    <div class="text-preview formatted">${highlightedFormatted}</div>
                </div>
                <div class="comparison-actions">
                    <button id="accept-scripture-formatting" class="control-button accent-button small">Accept Formatting</button>
                    <button id="reject-scripture-formatting" class="control-button secondary small">Reject Changes</button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        const acceptBtn = document.getElementById('accept-scripture-formatting');
        const rejectBtn = document.getElementById('reject-scripture-formatting');

        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                replaceSelectedTextWithMarkdown(formattedText);
                clearComparisonView();
                updateStatus("Scripture formatting applied successfully.", "success");
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                clearComparisonView();
                updateStatus("Scripture formatting rejected.", "info");
            });
        }
    }
}

/**
 * Highlight differences between original and formatted text
 */
function highlightTextDifferences(originalText, formattedText) {
    // Remove HTML tags from formatted text for comparison
    const cleanFormattedText = formattedText.replace(/<[^>]*>/g, '');

    console.log('Highlighting differences:');
    console.log('Original:', originalText);
    console.log('Formatted:', cleanFormattedText);

    // Simple approach: highlight key differences we know about
    let highlightedOriginal = originalText;
    let highlightedFormatted = cleanFormattedText;

    // Highlight common scripture formatting patterns

    // 1. Highlight "quote," additions at the beginning
    if (cleanFormattedText.toLowerCase().startsWith('quote,')) {
        highlightedFormatted = highlightedFormatted.replace(/^(quote,\s*)/i, '<span class="diff-added">$1</span>');
    }

    // 2. Highlight "end quote," additions
    highlightedFormatted = highlightedFormatted.replace(/(end quote,)/gi, '<span class="diff-added">$1</span>');

    // 3. Highlight book name changes (comprehensive Bible book coverage)
    const bookPatterns = [
        // Numbered books
        { original: /1\s*Pet\.?/gi, replacement: 'First Peter' },
        { original: /2\s*Pet\.?/gi, replacement: 'Second Peter' },
        { original: /1\s*Cor\.?/gi, replacement: 'First Corinthians' },
        { original: /2\s*Cor\.?/gi, replacement: 'Second Corinthians' },
        { original: /1\s*Sam\.?/gi, replacement: 'First Samuel' },
        { original: /2\s*Sam\.?/gi, replacement: 'Second Samuel' },
        { original: /1\s*Tim\.?/gi, replacement: 'First Timothy' },
        { original: /2\s*Tim\.?/gi, replacement: 'Second Timothy' },
        { original: /1\s*Thess\.?/gi, replacement: 'First Thessalonians' },
        { original: /2\s*Thess\.?/gi, replacement: 'Second Thessalonians' },
        { original: /1\s*Kgs\.?/gi, replacement: 'First Kings' },
        { original: /2\s*Kgs\.?/gi, replacement: 'Second Kings' },
        { original: /1\s*Chr\.?/gi, replacement: 'First Chronicles' },
        { original: /2\s*Chr\.?/gi, replacement: 'Second Chronicles' },
        { original: /1\s*Jn\.?/gi, replacement: 'First John' },
        { original: /2\s*Jn\.?/gi, replacement: 'Second John' },
        { original: /3\s*Jn\.?/gi, replacement: 'Third John' },
        { original: /3\s*John/gi, replacement: 'Third John' },

        // Old Testament
        { original: /Gen\.?/gi, replacement: 'Genesis' },
        { original: /Ex\.?/gi, replacement: 'Exodus' },
        { original: /Exod\.?/gi, replacement: 'Exodus' },
        { original: /Lev\.?/gi, replacement: 'Leviticus' },
        { original: /Num\.?/gi, replacement: 'Numbers' },
        { original: /Deut\.?/gi, replacement: 'Deuteronomy' },
        { original: /Josh\.?/gi, replacement: 'Joshua' },
        { original: /Judg\.?/gi, replacement: 'Judges' },
        { original: /Neh\.?/gi, replacement: 'Nehemiah' },
        { original: /Esth\.?/gi, replacement: 'Esther' },
        { original: /Ps\.?/gi, replacement: 'Psalms' },
        { original: /Psa\.?/gi, replacement: 'Psalms' },
        { original: /Prov\.?/gi, replacement: 'Proverbs' },
        { original: /Eccl\.?/gi, replacement: 'Ecclesiastes' },
        { original: /Ecc\.?/gi, replacement: 'Ecclesiastes' },
        { original: /Is\.?/gi, replacement: 'Isaiah' },
        { original: /Isa\.?/gi, replacement: 'Isaiah' },
        { original: /Jer\.?/gi, replacement: 'Jeremiah' },
        { original: /Lam\.?/gi, replacement: 'Lamentations' },
        { original: /Ezek\.?/gi, replacement: 'Ezekiel' },
        { original: /Ez\.?/gi, replacement: 'Ezekiel' },
        { original: /Dan\.?/gi, replacement: 'Daniel' },
        { original: /Hos\.?/gi, replacement: 'Hosea' },
        { original: /Obad\.?/gi, replacement: 'Obadiah' },
        { original: /Jon\.?/gi, replacement: 'Jonah' },
        { original: /Mic\.?/gi, replacement: 'Micah' },
        { original: /Nah\.?/gi, replacement: 'Nahum' },
        { original: /Hab\.?/gi, replacement: 'Habakkuk' },
        { original: /Zeph\.?/gi, replacement: 'Zephaniah' },
        { original: /Hag\.?/gi, replacement: 'Haggai' },
        { original: /Zech\.?/gi, replacement: 'Zechariah' },
        { original: /Mal\.?/gi, replacement: 'Malachi' },

        // New Testament
        { original: /Mt\.?/gi, replacement: 'Matthew' },
        { original: /Matt\.?/gi, replacement: 'Matthew' },
        { original: /Mk\.?/gi, replacement: 'Mark' },
        { original: /Lk\.?/gi, replacement: 'Luke' },
        { original: /Jn\.?/gi, replacement: 'John' },
        { original: /Rom\.?/gi, replacement: 'Romans' },
        { original: /Gal\.?/gi, replacement: 'Galatians' },
        { original: /Eph\.?/gi, replacement: 'Ephesians' },
        { original: /Phil\.?/gi, replacement: 'Philippians' },
        { original: /Col\.?/gi, replacement: 'Colossians' },
        { original: /Tit\.?/gi, replacement: 'Titus' },
        { original: /Philem\.?/gi, replacement: 'Philemon' },
        { original: /Heb\.?/gi, replacement: 'Hebrews' },
        { original: /Jas\.?/gi, replacement: 'James' },
        { original: /Rev\.?/gi, replacement: 'Revelation' }
    ];

    bookPatterns.forEach(pattern => {
        const originalMatch = originalText.match(pattern.original);
        if (originalMatch && cleanFormattedText.includes(pattern.replacement)) {
            // Highlight the original
            highlightedOriginal = highlightedOriginal.replace(pattern.original, '<span class="diff-changed">$&</span>');
            // Highlight the replacement
            highlightedFormatted = highlightedFormatted.replace(new RegExp(pattern.replacement, 'gi'), '<span class="diff-changed">$&</span>');
        }
    });

    // 4. Highlight chapter/verse formatting changes
    const chapterVersePattern = /(\d+),?\s*(\d+)/g;
    const originalMatch = originalText.match(chapterVersePattern);
    if (originalMatch) {
        originalMatch.forEach(match => {
            const [, chapter, verse] = match.match(/(\d+),?\s*(\d+)/);
            const expandedForm = `chapter ${chapter}, verse ${verse}`;

            if (cleanFormattedText.includes(expandedForm)) {
                // Highlight original chapter/verse
                highlightedOriginal = highlightedOriginal.replace(match, `<span class="diff-changed">${match}</span>`);
                // Highlight expanded form
                highlightedFormatted = highlightedFormatted.replace(expandedForm, `<span class="diff-changed">${expandedForm}</span>`);
            }
        });
    }

    // 5. Highlight "chapter" and "verse" words when they're added
    highlightedFormatted = highlightedFormatted.replace(/\b(chapter|verse)\b/gi, '<span class="diff-added">$1</span>');

    console.log('Highlighted original:', highlightedOriginal);
    console.log('Highlighted formatted:', highlightedFormatted);

    return {
        highlightedOriginal: highlightedOriginal,
        highlightedFormatted: highlightedFormatted
    };
}

/**
 * Check if this is a book name expansion (e.g., "1 Pet." → "First Peter")
 */
function isBookNameExpansion(original, formatted) {
    const bookMappings = {
        '1': 'first', '2': 'second', '3': 'third',
        'pet.': 'peter', 'pet': 'peter',
        'cor.': 'corinthians', 'cor': 'corinthians',
        'sam.': 'samuel', 'sam': 'samuel',
        'lev.': 'leviticus', 'lev': 'leviticus',
        'gen.': 'genesis', 'gen': 'genesis',
        'tim.': 'timothy', 'tim': 'timothy',
        'thess.': 'thessalonians', 'thess': 'thessalonians'
    };

    const originalLower = original.toLowerCase();
    const formattedLower = formatted.toLowerCase();

    return bookMappings[originalLower] === formattedLower;
}

/**
 * Check if this is a chapter/verse expansion
 */
function isChapterVerseExpansion(originalWords, formattedWords, originalIndex, formattedIndex) {
    // Check if we have enough words to compare
    if (originalIndex + 2 >= originalWords.length || formattedIndex + 4 >= formattedWords.length) {
        return false;
    }

    // Look for pattern: number, comma, number → "chapter", number, comma, "verse", number
    const original = originalWords.slice(originalIndex, originalIndex + 3).join('').toLowerCase();
    const formatted = formattedWords.slice(formattedIndex, formattedIndex + 5).join('').toLowerCase();

    // Simple pattern matching for chapter/verse
    return /^\d+,\s*\d+$/.test(original.replace(/\s/g, '')) &&
           formatted.includes('chapter') && formatted.includes('verse');
}

/**
 * Clear the comparison view and return to normal selection display
 */
function clearComparisonView() {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');
    const modal = document.getElementById('text-expansion-preview-modal');

    if (selectionDisplay) {
        selectionDisplay.innerHTML = `
            <div class="no-selection-state">
                <p>No text selected</p>
                <p class="hint">Select text in the document to expand abbreviations</p>
            </div>
        `;
    }

    if (modal) {
        modal.style.display = 'none';
    }
}

// Placeholder functions - these will be implemented in separate files
// due to their complexity and size

/**
 * Format scripture references according to the specified rules
 */
export function formatScriptureReferences(text) {
    console.log('Formatting scripture references...');
    console.log('Input text:', JSON.stringify(text));

    // Bible book abbreviations mapping to full names
    const bookAbbreviations = {
        // Old Testament
        'Gen.': 'Genesis', 'Gen': 'Genesis',
        'Ex.': 'Exodus', 'Exod.': 'Exodus', 'Exod': 'Exodus',
        'Lev.': 'Leviticus', 'Lev': 'Leviticus',
        'Num.': 'Numbers', 'Num': 'Numbers',
        'Deut.': 'Deuteronomy', 'Deut': 'Deuteronomy',
        'Josh.': 'Joshua', 'Josh': 'Joshua',
        'Judg.': 'Judges', 'Judg': 'Judges',
        'Ruth': 'Ruth',
        '1 Sam.': '1 Samuel', '1Sam.': '1 Samuel', '1Sam': '1 Samuel',
        '2 Sam.': '2 Samuel', '2Sam.': '2 Samuel', '2Sam': '2 Samuel',
        '1 Kings': '1 Kings', '1Kgs.': '1 Kings', '1Kgs': '1 Kings',
        '2 Kings': '2 Kings', '2Kgs.': '2 Kings', '2Kgs': '2 Kings',
        '1 Chr.': '1 Chronicles', '1Chr.': '1 Chronicles', '1Chr': '1 Chronicles',
        '2 Chr.': '2 Chronicles', '2Chr.': '2 Chronicles', '2Chr': '2 Chronicles',
        'Ezra': 'Ezra',
        'Neh.': 'Nehemiah', 'Neh': 'Nehemiah',
        'Esth.': 'Esther', 'Esth': 'Esther',
        'Job': 'Job',
        'Ps.': 'Psalms', 'Ps': 'Psalms', 'Psa.': 'Psalms', 'Psa': 'Psalms',
        'Prov.': 'Proverbs', 'Prov': 'Proverbs',
        'Eccl.': 'Ecclesiastes', 'Eccl': 'Ecclesiastes', 'Ecc.': 'Ecclesiastes', 'Ecc': 'Ecclesiastes',
        'Song': 'Song of Songs', 'SS': 'Song of Songs',
        'Is.': 'Isaiah', 'Isa.': 'Isaiah', 'Isa': 'Isaiah',
        'Jer.': 'Jeremiah', 'Jer': 'Jeremiah',
        'Lam.': 'Lamentations', 'Lam': 'Lamentations',
        'Ezek.': 'Ezekiel', 'Ezek': 'Ezekiel', 'Ez.': 'Ezekiel', 'Ez': 'Ezekiel',
        'Dan.': 'Daniel', 'Dan': 'Daniel',
        'Hos.': 'Hosea', 'Hos': 'Hosea',
        'Joel': 'Joel',
        'Amos': 'Amos',
        'Obad.': 'Obadiah', 'Obad': 'Obadiah',
        'Jon.': 'Jonah', 'Jon': 'Jonah',
        'Mic.': 'Micah', 'Mic': 'Micah',
        'Nah.': 'Nahum', 'Nah': 'Nahum',
        'Hab.': 'Habakkuk', 'Hab': 'Habakkuk',
        'Zeph.': 'Zephaniah', 'Zeph': 'Zephaniah',
        'Hag.': 'Haggai', 'Hag': 'Haggai',
        'Zech.': 'Zechariah', 'Zech': 'Zechariah',
        'Mal.': 'Malachi', 'Mal': 'Malachi',

        // New Testament
        'Mt.': 'Matthew', 'Matt.': 'Matthew', 'Matt': 'Matthew',
        'Mk.': 'Mark', 'Mark': 'Mark',
        'Lk.': 'Luke', 'Luke': 'Luke',
        'Jn.': 'John', 'John': 'John',
        'Acts': 'Acts',
        'Rom.': 'Romans', 'Rom': 'Romans',
        '1 Cor.': '1 Corinthians', '1Cor.': '1 Corinthians', '1Cor': '1 Corinthians',
        '2 Cor.': '2 Corinthians', '2Cor.': '2 Corinthians', '2Cor': '2 Corinthians',
        'Gal.': 'Galatians', 'Gal': 'Galatians',
        'Eph.': 'Ephesians', 'Eph': 'Ephesians',
        'Phil.': 'Philippians', 'Phil': 'Philippians',
        'Col.': 'Colossians', 'Col': 'Colossians',
        '1 Thess.': '1 Thessalonians', '1Thess.': '1 Thessalonians', '1Thess': '1 Thessalonians',
        '2 Thess.': '2 Thessalonians', '2Thess.': '2 Thessalonians', '2Thess': '2 Thessalonians',
        '1 Tim.': '1 Timothy', '1Tim.': '1 Timothy', '1Tim': '1 Timothy',
        '2 Tim.': '2 Timothy', '2Tim.': '2 Timothy', '2Tim': '2 Timothy',
        'Tit.': 'Titus', 'Tit': 'Titus',
        'Philem.': 'Philemon', 'Philem': 'Philemon',
        'Heb.': 'Hebrews', 'Heb': 'Hebrews',
        'Jas.': 'James', 'Jas': 'James',
        '1 Pet.': '1 Peter', '1Pet.': '1 Peter', '1Pet': '1 Peter',
        '2 Pet.': '2 Peter', '2Pet.': '2 Peter', '2Pet': '2 Peter',
        '1 Jn.': '1 John', '1John': '1 John',
        '2 Jn.': '2 John', '2John': '2 John',
        '3 Jn.': '3 John', '3John': '3 John',
        'Jude': 'Jude',
        'Rev.': 'Revelation', 'Rev': 'Revelation'
    };

    // Pattern to match quoted text followed by scripture reference
    // Matches various quote types: 'text' "text" 'text' "text" etc.
    const scripturePattern = /(['""''])((?:(?!\1).){8,}?)\1\s*\(([^)]+)\)/g;

    console.log('Testing pattern against text...');
    console.log('Pattern:', scripturePattern);

    let formattedText = text;
    let hasChanges = false;

    formattedText = formattedText.replace(scripturePattern, (match, quote, quotedText, reference) => {
        // Check if the quoted text has 8 or more words
        const wordCount = quotedText.trim().split(/\s+/).length;
        if (wordCount < 8) {
            return match; // Don't format if less than 8 words
        }

        // Expand book abbreviations in the reference
        let expandedReference = reference;
        for (const [abbrev, fullName] of Object.entries(bookAbbreviations)) {
            const regex = new RegExp(`\\b${abbrev.replace('.', '\\.')}\\b`, 'gi');
            expandedReference = expandedReference.replace(regex, fullName);
        }

        // Format chapter and verse numbers with "chapter" and "verse" words
        // Pattern: BookName number, number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+),?\s*(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number:number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+):(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number -> BookName, chapter number (for chapter-only references)
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+)$/, '$1, chapter $2');

        hasChanges = true;

        // Format as: quote, [quoted text] end quote, ([expanded reference])
        return `<strong>quote, ${quotedText} end quote, (${expandedReference})</strong>`;
    });

    if (hasChanges) {
        console.log('Scripture references formatted successfully');
    } else {
        console.log('No scripture references found to format');
    }

    return formattedText;
}

/**
 * Format quotes from written works according to the specified rules
 */
export function formatQuotesFromWrittenWorks(text) {
    // This function will be moved to a separate quotes.js file
    // For now, return the original text
    console.log('Quote formatting not yet implemented in modular structure');
    return text;
}

/**
 * Expand all abbreviations in the text
 */
export function expandAllAbbreviations(text) {
    // This function will be moved to a separate abbreviations.js file
    // For now, return the original text
    console.log('Abbreviation expansion not yet implemented in modular structure');
    return text;
}

/**
 * Format papal names and saint abbreviations
 */
export function formatPapalNamesAndSaints(text) {
    // This function will be moved to a separate papal.js file
    // For now, return the original text
    console.log('Papal/saint formatting not yet implemented in modular structure');
    return text;
}

/**
 * Format footnotes for audiobook narration
 */
export function formatFootnotesForAudiobook(text) {
    // This function will be moved to a separate footnotes.js file
    // For now, return the original text
    console.log('Footnote formatting not yet implemented in modular structure');
    return text;
}
