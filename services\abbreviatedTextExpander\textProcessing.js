/********************************************************************
 *  textProcessing.js - Text Processing Module
 *  ---------------------------------------------------------------
 *  All text formatting and processing functions
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, updateStatus, handleDocumentChange } from './core.js';

/**
 * Initialize text processing
 */
export function initializeTextProcessing() {
    console.log('Initializing text processing...');
    
    // Set up event listeners for all formatting buttons
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.addEventListener('click', handleFormatScripture);
    }
    
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.addEventListener('click', handleFormatQuotes);
    }
    
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.addEventListener('click', handleExpandAbbreviations);
    }
    
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.addEventListener('click', handleFormatPapalSaints);
    }
    
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.addEventListener('click', handleFormatFootnotes);
    }
}

/**
 * Handle scripture formatting
 */
function handleFormatScripture() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Formatting scripture references...", "info");

        // Get the selected text
        const selectedText = getSelectedText();

        if (!selectedText) {
            updateStatus("Please select some text to format scripture references.", "error");
            return;
        }

        // Apply scripture formatting to selected text
        const formattedContent = formatScriptureReferences(selectedText);

        if (formattedContent !== selectedText) {
            // Replace the selected text with formatted version
            replaceSelectedText(formattedContent);

            updateStatus("Scripture references formatted successfully.", "success");
        } else {
            updateStatus("No scripture references found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting scripture:", error);
        updateStatus(`Failed to format scripture: ${error.message}`, "error");
    }
}

/**
 * Handle quote formatting
 */
function handleFormatQuotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting quotes from written works...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply quote formatting
        const formattedContent = formatQuotesFromWrittenWorks(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.innerHTML = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Quotes from written works formatted successfully.", "success");
        } else {
            updateStatus("No qualifying quotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting quotes:", error);
        updateStatus(`Failed to format quotes: ${error.message}`, "error");
    }
}

/**
 * Handle abbreviation expansion
 */
function handleExpandAbbreviations() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Expanding abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply abbreviation expansion
        const expandedContent = expandAllAbbreviations(content);
        
        if (expandedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = expandedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Abbreviations expanded successfully.", "success");
        } else {
            updateStatus("No abbreviations found to expand.", "info");
        }
    } catch (error) {
        console.error("Error expanding abbreviations:", error);
        updateStatus(`Failed to expand abbreviations: ${error.message}`, "error");
    }
}

/**
 * Handle papal saints formatting
 */
function handleFormatPapalSaints() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting papal names and saint abbreviations...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply papal saints formatting
        const formattedContent = formatPapalNamesAndSaints(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Papal names and saint abbreviations formatted successfully.", "success");
        } else {
            updateStatus("No papal names or saint abbreviations found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting papal names and saints:", error);
        updateStatus(`Failed to format papal names and saints: ${error.message}`, "error");
    }
}

/**
 * Handle footnote formatting
 */
function handleFormatFootnotes() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    try {
        updateStatus("Formatting footnotes for audiobook...", "info");
        
        // Get the current document content
        let content = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
        
        // Apply footnote formatting
        const formattedContent = formatFootnotesForAudiobook(content);
        
        if (formattedContent !== content) {
            // Update the document
            dom.abbreviatedTextExpanderTextArea.textContent = formattedContent;
            
            // Mark as modified and add to history
            handleDocumentChange();
            
            updateStatus("Footnotes formatted for audiobook successfully.", "success");
        } else {
            updateStatus("No footnotes found to format.", "info");
        }
    } catch (error) {
        console.error("Error formatting footnotes:", error);
        updateStatus(`Failed to format footnotes: ${error.message}`, "error");
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Create a document fragment to hold the formatted content
        const fragment = document.createDocumentFragment();

        // If the formatted text contains HTML, we need to parse it
        if (formattedText.includes('<strong>')) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedText;

            // Move all child nodes to the fragment
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }
        } else {
            // Plain text - create a text node
            fragment.appendChild(document.createTextNode(formattedText));
        }

        // Insert the formatted content
        range.insertNode(fragment);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Text replaced successfully');
    }
}

// Placeholder functions - these will be implemented in separate files
// due to their complexity and size

/**
 * Format scripture references according to the specified rules
 */
export function formatScriptureReferences(text) {
    console.log('Formatting scripture references...');

    // Bible book abbreviations mapping to full names
    const bookAbbreviations = {
        // Old Testament
        'Gen.': 'Genesis', 'Gen': 'Genesis',
        'Ex.': 'Exodus', 'Exod.': 'Exodus', 'Exod': 'Exodus',
        'Lev.': 'Leviticus', 'Lev': 'Leviticus',
        'Num.': 'Numbers', 'Num': 'Numbers',
        'Deut.': 'Deuteronomy', 'Deut': 'Deuteronomy',
        'Josh.': 'Joshua', 'Josh': 'Joshua',
        'Judg.': 'Judges', 'Judg': 'Judges',
        'Ruth': 'Ruth',
        '1 Sam.': '1 Samuel', '1Sam.': '1 Samuel', '1Sam': '1 Samuel',
        '2 Sam.': '2 Samuel', '2Sam.': '2 Samuel', '2Sam': '2 Samuel',
        '1 Kings': '1 Kings', '1Kgs.': '1 Kings', '1Kgs': '1 Kings',
        '2 Kings': '2 Kings', '2Kgs.': '2 Kings', '2Kgs': '2 Kings',
        '1 Chr.': '1 Chronicles', '1Chr.': '1 Chronicles', '1Chr': '1 Chronicles',
        '2 Chr.': '2 Chronicles', '2Chr.': '2 Chronicles', '2Chr': '2 Chronicles',
        'Ezra': 'Ezra',
        'Neh.': 'Nehemiah', 'Neh': 'Nehemiah',
        'Esth.': 'Esther', 'Esth': 'Esther',
        'Job': 'Job',
        'Ps.': 'Psalms', 'Ps': 'Psalms', 'Psa.': 'Psalms', 'Psa': 'Psalms',
        'Prov.': 'Proverbs', 'Prov': 'Proverbs',
        'Eccl.': 'Ecclesiastes', 'Eccl': 'Ecclesiastes', 'Ecc.': 'Ecclesiastes', 'Ecc': 'Ecclesiastes',
        'Song': 'Song of Songs', 'SS': 'Song of Songs',
        'Is.': 'Isaiah', 'Isa.': 'Isaiah', 'Isa': 'Isaiah',
        'Jer.': 'Jeremiah', 'Jer': 'Jeremiah',
        'Lam.': 'Lamentations', 'Lam': 'Lamentations',
        'Ezek.': 'Ezekiel', 'Ezek': 'Ezekiel', 'Ez.': 'Ezekiel', 'Ez': 'Ezekiel',
        'Dan.': 'Daniel', 'Dan': 'Daniel',
        'Hos.': 'Hosea', 'Hos': 'Hosea',
        'Joel': 'Joel',
        'Amos': 'Amos',
        'Obad.': 'Obadiah', 'Obad': 'Obadiah',
        'Jon.': 'Jonah', 'Jon': 'Jonah',
        'Mic.': 'Micah', 'Mic': 'Micah',
        'Nah.': 'Nahum', 'Nah': 'Nahum',
        'Hab.': 'Habakkuk', 'Hab': 'Habakkuk',
        'Zeph.': 'Zephaniah', 'Zeph': 'Zephaniah',
        'Hag.': 'Haggai', 'Hag': 'Haggai',
        'Zech.': 'Zechariah', 'Zech': 'Zechariah',
        'Mal.': 'Malachi', 'Mal': 'Malachi',

        // New Testament
        'Mt.': 'Matthew', 'Matt.': 'Matthew', 'Matt': 'Matthew',
        'Mk.': 'Mark', 'Mark': 'Mark',
        'Lk.': 'Luke', 'Luke': 'Luke',
        'Jn.': 'John', 'John': 'John',
        'Acts': 'Acts',
        'Rom.': 'Romans', 'Rom': 'Romans',
        '1 Cor.': '1 Corinthians', '1Cor.': '1 Corinthians', '1Cor': '1 Corinthians',
        '2 Cor.': '2 Corinthians', '2Cor.': '2 Corinthians', '2Cor': '2 Corinthians',
        'Gal.': 'Galatians', 'Gal': 'Galatians',
        'Eph.': 'Ephesians', 'Eph': 'Ephesians',
        'Phil.': 'Philippians', 'Phil': 'Philippians',
        'Col.': 'Colossians', 'Col': 'Colossians',
        '1 Thess.': '1 Thessalonians', '1Thess.': '1 Thessalonians', '1Thess': '1 Thessalonians',
        '2 Thess.': '2 Thessalonians', '2Thess.': '2 Thessalonians', '2Thess': '2 Thessalonians',
        '1 Tim.': '1 Timothy', '1Tim.': '1 Timothy', '1Tim': '1 Timothy',
        '2 Tim.': '2 Timothy', '2Tim.': '2 Timothy', '2Tim': '2 Timothy',
        'Tit.': 'Titus', 'Tit': 'Titus',
        'Philem.': 'Philemon', 'Philem': 'Philemon',
        'Heb.': 'Hebrews', 'Heb': 'Hebrews',
        'Jas.': 'James', 'Jas': 'James',
        '1 Pet.': '1 Peter', '1Pet.': '1 Peter', '1Pet': '1 Peter',
        '2 Pet.': '2 Peter', '2Pet.': '2 Peter', '2Pet': '2 Peter',
        '1 Jn.': '1 John', '1John': '1 John',
        '2 Jn.': '2 John', '2John': '2 John',
        '3 Jn.': '3 John', '3John': '3 John',
        'Jude': 'Jude',
        'Rev.': 'Revelation', 'Rev': 'Revelation'
    };

    // Pattern to match quoted text followed by scripture reference
    // Matches: 'text' (Book chapter, verse) or "text" (Book chapter, verse)
    const scripturePattern = /(['"])((?:(?!\1).){8,}?)\1\s*\(([^)]+)\)/g;

    let formattedText = text;
    let hasChanges = false;

    formattedText = formattedText.replace(scripturePattern, (match, quote, quotedText, reference) => {
        // Check if the quoted text has 8 or more words
        const wordCount = quotedText.trim().split(/\s+/).length;
        if (wordCount < 8) {
            return match; // Don't format if less than 8 words
        }

        // Expand book abbreviations in the reference
        let expandedReference = reference;
        for (const [abbrev, fullName] of Object.entries(bookAbbreviations)) {
            const regex = new RegExp(`\\b${abbrev.replace('.', '\\.')}\\b`, 'gi');
            expandedReference = expandedReference.replace(regex, fullName);
        }

        // Format chapter and verse numbers with "chapter" and "verse" words
        // Pattern: BookName number, number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+),?\s*(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number:number -> BookName, chapter number, verse number
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+):(\d+)$/, '$1, chapter $2, verse $3');
        // Pattern: BookName number -> BookName, chapter number (for chapter-only references)
        expandedReference = expandedReference.replace(/^([^0-9]+)\s*(\d+)$/, '$1, chapter $2');

        hasChanges = true;

        // Format as: quote, [quoted text] end quote, ([expanded reference])
        return `<strong>quote, ${quotedText} end quote, (${expandedReference})</strong>`;
    });

    if (hasChanges) {
        console.log('Scripture references formatted successfully');
    } else {
        console.log('No scripture references found to format');
    }

    return formattedText;
}

/**
 * Format quotes from written works according to the specified rules
 */
export function formatQuotesFromWrittenWorks(text) {
    // This function will be moved to a separate quotes.js file
    // For now, return the original text
    console.log('Quote formatting not yet implemented in modular structure');
    return text;
}

/**
 * Expand all abbreviations in the text
 */
export function expandAllAbbreviations(text) {
    // This function will be moved to a separate abbreviations.js file
    // For now, return the original text
    console.log('Abbreviation expansion not yet implemented in modular structure');
    return text;
}

/**
 * Format papal names and saint abbreviations
 */
export function formatPapalNamesAndSaints(text) {
    // This function will be moved to a separate papal.js file
    // For now, return the original text
    console.log('Papal/saint formatting not yet implemented in modular structure');
    return text;
}

/**
 * Format footnotes for audiobook narration
 */
export function formatFootnotesForAudiobook(text) {
    // This function will be moved to a separate footnotes.js file
    // For now, return the original text
    console.log('Footnote formatting not yet implemented in modular structure');
    return text;
}
