/********************************************************************
 *  uiHandlers.js - UI Handlers Module
 *  ---------------------------------------------------------------
 *  Event handlers and UI management for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, updateStatus, handleDocumentChange } from './core.js';
import { 
    expandAllAbbreviations,
    formatPapalNamesAndSaints,
    formatScriptureReferences,
    formatQuotesFromWrittenWorks,
    formatFootnotesForAudiobook
} from './textProcessing.js';

// Global variable to track if run all is cancelled
let isRunAllCancelled = false;

/**
 * Initialize UI handlers
 */
export function initializeUIHandlers() {
    console.log('Initializing UI handlers...');
    
    // Set up run all formatting functionality
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.addEventListener('click', handleRunAllFormatting);
    }
    
    if (dom.cancelRunAllBtn) {
        dom.cancelRunAllBtn.addEventListener('click', handleCancelRunAll);
    }
}

/**
 * Handle run all formatting
 */
async function handleRunAllFormatting() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }
    
    // Reset cancellation flag
    isRunAllCancelled = false;
    
    // Show progress indicator
    showRunAllProgress();
    
    // Define the formatting functions in execution order
    const formattingSteps = [
        {
            name: "Expand All Abbreviations",
            function: expandAllAbbreviations,
            description: "Expanding common abbreviations..."
        },
        {
            name: "Format Papal & Saint Names",
            function: formatPapalNamesAndSaints,
            description: "Formatting papal and saint names..."
        },
        {
            name: "Format Scripture References",
            function: formatScriptureReferences,
            description: "Formatting scripture references..."
        },
        {
            name: "Add Quote End Quote",
            function: formatQuotesFromWrittenWorks,
            description: "Adding quote markers..."
        },
        {
            name: "Format Footnotes for Audiobook",
            function: formatFootnotesForAudiobook,
            description: "Formatting footnotes..."
        }
    ];
    
    const results = [];
    let currentContent = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
    
    try {
        for (let i = 0; i < formattingSteps.length; i++) {
            if (isRunAllCancelled) {
                updateRunAllProgress(0, "Cancelled", "Operation cancelled by user");
                hideRunAllProgress();
                return;
            }
            
            const step = formattingSteps[i];
            const stepNumber = i + 1;
            const totalSteps = formattingSteps.length;
            
            // Update progress
            updateRunAllProgress(
                (stepNumber - 1) / totalSteps * 100,
                `Step ${stepNumber} of ${totalSteps}`,
                step.description
            );
            
            try {
                // Apply the formatting function
                const originalContent = currentContent;
                currentContent = step.function(currentContent);
                
                // Check if changes were made
                const hasChanges = currentContent !== originalContent;
                
                results.push({
                    step: step.name,
                    success: true,
                    hasChanges: hasChanges,
                    error: null
                });
                
                // Small delay to show progress
                await new Promise(resolve => setTimeout(resolve, 300));
                
            } catch (error) {
                console.error(`Error in ${step.name}:`, error);
                results.push({
                    step: step.name,
                    success: false,
                    hasChanges: false,
                    error: error.message
                });
            }
        }
        
        // Update the document with final content
        if (currentContent !== (dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText)) {
            dom.abbreviatedTextExpanderTextArea.textContent = currentContent;
            handleDocumentChange();
        }
        
        // Show completion
        updateRunAllProgress(100, "Complete", "All formatting steps completed");
        
        // Generate summary
        const summary = generateRunAllSummary(results);
        updateStatus(summary, "success");
        
        // Hide progress after a delay
        setTimeout(() => {
            hideRunAllProgress();
        }, 2000);
        
    } catch (error) {
        console.error("Error in run all formatting:", error);
        updateStatus(`Failed to complete all formatting: ${error.message}`, "error");
        hideRunAllProgress();
    }
}

/**
 * Handle cancel run all
 */
function handleCancelRunAll() {
    isRunAllCancelled = true;
    updateStatus("Formatting operation cancelled.", "info");
}

/**
 * Show run all progress indicator
 */
function showRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'block';
    }
    
    // Disable the run all button
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = true;
    }
}

/**
 * Hide run all progress indicator
 */
function hideRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'none';
    }
    
    // Re-enable the run all button
    if (dom.runAllFormattingBtn && getCurrentDocument()) {
        dom.runAllFormattingBtn.disabled = false;
    }
}

/**
 * Update run all progress
 */
function updateRunAllProgress(percentage, title, description) {
    if (dom.progressFill) {
        dom.progressFill.style.width = `${percentage}%`;
    }
    
    if (dom.progressText) {
        dom.progressText.innerHTML = `<strong>${title}</strong><br>${description}`;
    }
}

/**
 * Generate summary of run all results
 */
function generateRunAllSummary(results) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const withChanges = results.filter(r => r.success && r.hasChanges);
    
    let summary = `Run All Formatting Complete: ${successful.length}/${results.length} steps successful`;
    
    if (withChanges.length > 0) {
        summary += `. Changes applied: ${withChanges.map(r => r.step).join(', ')}`;
    }
    
    if (failed.length > 0) {
        summary += `. Failed: ${failed.map(r => r.step).join(', ')}`;
    }
    
    return summary;
}

/**
 * Initialize dropdown functionality for buttons
 */
export function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('.button-with-dropdown');
    
    dropdownButtons.forEach(container => {
        const button = container.querySelector('button');
        const dropdownToggle = container.querySelector('.dropdown-toggle');
        
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                // Close other dropdowns
                dropdownButtons.forEach(otherContainer => {
                    if (otherContainer !== container) {
                        otherContainer.classList.remove('expanded');
                    }
                });
                
                // Toggle current dropdown
                container.classList.toggle('expanded');
            });
        }
        
        // Prevent button click when clicking dropdown toggle
        if (button && dropdownToggle) {
            button.addEventListener('click', (e) => {
                if (e.target === dropdownToggle || dropdownToggle.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.button-with-dropdown')) {
            dropdownButtons.forEach(container => {
                container.classList.remove('expanded');
            });
        }
    });
}
